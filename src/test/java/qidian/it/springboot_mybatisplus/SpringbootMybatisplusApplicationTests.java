package qidian.it.springboot_mybatisplus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import qidian.it.springboot_mybatisplus.dao.BookDao;
import qidian.it.springboot_mybatisplus.domain.Book;

@SpringBootTest
class SpringbootMybatisplusApplicationTests {

    @Autowired
    private BookDao bookDao;

    //分页
    @Test
    void testGetPage() {
        IPage page = new Page(1, 2);
        bookDao.selectPage(page, null);
        System.out.println("当前页码值：" + page.getCurrent() + "，总页码数：" + page.getPages() + "，每页显示数：" + page.getSize() + "，总记录数：" + page.getTotal() + "，当前页数据：" + page.getRecords());
    }


    @Test
    void testGetAll() {
        bookDao.selectList(null).forEach(System.out::println);
    }
    @Test
    void testAdd() {
        bookDao.insert(new Book(null, "java从入门到放弃", "king"));
//        Book book = new Book();
//        book.setName("java从入门到放弃");
//        book.setAuthor("king");
//        bookDao.insert(book);
        //更直观
    }

    @Test
    void delete() {
        bookDao.deleteById(1441968129);
    }
    //修改
    @Test
    void update() {
        Book book = new Book();
        book.setId(111);
        book.setName("java从入门到入土");
        bookDao.updateById(book);
    }

}
