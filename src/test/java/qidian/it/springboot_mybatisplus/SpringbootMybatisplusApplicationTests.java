package qidian.it.springboot_mybatisplus;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import qidian.it.springboot_mybatisplus.dao.BookDao;
import qidian.it.springboot_mybatisplus.domain.Book;

@SpringBootTest
class SpringbootMybatisplusApplicationTests {

    @Autowired
    private BookDao bookDao;
    @Test
    void testGetAll() {
        bookDao.selectList(null).forEach(System.out::println);
    }
    @Test
    void testAdd() {
        bookDao.insert(new Book(null, "java从入门到放弃", "king"));
//        Book book = new Book();
//        book.setName("java从入门到放弃");
//        book.setAuthor("king");
//        bookDao.insert(book);
        //更直观
    }

    @Test
    void delete() {
        bookDao.deleteById(1441968129);
    }
    //修改
    @Test
    void update() {
        Book book = new Book();
        book.setId(111);
        book.setName("java从入门到入土");
        bookDao.updateById(book);
    }

}
